-- Supabase Database Schema for Course Data Import
-- This schema is based on the CSV structure provided

-- Main courses table
CREATE TABLE IF NOT EXISTS courses (
  id BIGSERIAL PRIMARY KEY,
  category_name TEXT,
  chapter_name TEXT,
  sub_category_name TEXT,
  title TEXT NOT NULL,
  url TEXT,
  embed_url TEXT NOT NULL,
  video_id TEXT NOT NULL UNIQUE,
  thumbnail_url TEXT,
  is_premium BOOLEAN DEFAULT FALSE,
  is_published BOOLEAN DEFAULT TRUE,
  author_url TEXT,
  created_at TIMESTAMPTZ,
  imported_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_courses_category_name ON courses(category_name);
CREATE INDEX IF NOT EXISTS idx_courses_chapter_name ON courses(chapter_name);
CREATE INDEX IF NOT EXISTS idx_courses_sub_category_name ON courses(sub_category_name);
CREATE INDEX IF NOT EXISTS idx_courses_video_id ON courses(video_id);
CREATE INDEX IF NOT EXISTS idx_courses_is_premium ON courses(is_premium);
CREATE INDEX IF NOT EXISTS idx_courses_is_published ON courses(is_published);
CREATE INDEX IF NOT EXISTS idx_courses_created_at ON courses(created_at);

-- Optional: Normalized schema with separate tables for better data organization
-- Uncomment if you prefer a normalized approach

/*
-- Categories table
CREATE TABLE IF NOT EXISTS categories (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Subjects table
CREATE TABLE IF NOT EXISTS subjects (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Chapters table
CREATE TABLE IF NOT EXISTS chapters (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  category_id BIGINT REFERENCES categories(id),
  subject_id BIGINT REFERENCES subjects(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Authors table
CREATE TABLE IF NOT EXISTS authors (
  id BIGSERIAL PRIMARY KEY,
  name TEXT,
  url TEXT UNIQUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Normalized courses table
CREATE TABLE IF NOT EXISTS courses_normalized (
  id BIGSERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  url TEXT,
  embed_url TEXT NOT NULL,
  video_id TEXT NOT NULL UNIQUE,
  thumbnail_url TEXT,
  is_premium BOOLEAN DEFAULT FALSE,
  is_published BOOLEAN DEFAULT TRUE,
  category_id BIGINT REFERENCES categories(id),
  subject_id BIGINT REFERENCES subjects(id),
  chapter_id BIGINT REFERENCES chapters(id),
  author_id BIGINT REFERENCES authors(id),
  created_at TIMESTAMPTZ,
  imported_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
*/

-- Row Level Security (RLS) policies
-- Uncomment and modify as needed for your security requirements

/*
-- Enable RLS
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;

-- Policy for authenticated users to read all courses
CREATE POLICY "Allow authenticated users to read courses" ON courses
  FOR SELECT TO authenticated USING (true);

-- Policy for service role to insert/update/delete
CREATE POLICY "Allow service role full access" ON courses
  FOR ALL TO service_role USING (true);

-- Policy for public to read published courses only
CREATE POLICY "Allow public to read published courses" ON courses
  FOR SELECT TO anon USING (is_published = true);
*/

-- Trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_courses_updated_at 
  BEFORE UPDATE ON courses 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Sample queries for testing

-- Count courses by category
-- SELECT category_name, COUNT(*) as course_count 
-- FROM courses 
-- GROUP BY category_name 
-- ORDER BY course_count DESC;

-- Count courses by subject
-- SELECT sub_category_name, COUNT(*) as course_count 
-- FROM courses 
-- GROUP BY sub_category_name 
-- ORDER BY course_count DESC;

-- Find premium courses
-- SELECT title, category_name, sub_category_name 
-- FROM courses 
-- WHERE is_premium = true 
-- ORDER BY created_at DESC;

-- Find unpublished courses
-- SELECT title, category_name, sub_category_name 
-- FROM courses 
-- WHERE is_published = false 
-- ORDER BY created_at DESC;
