#!/usr/bin/env node

const fs = require('fs');
const csv = require('csv-parser');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const config = {
  supabaseUrl: process.env.SUPABASE_URL,
  supabaseKey: process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY,
  csvFilePath: process.env.CSV_FILE_PATH || './csv-source',
  batchSize: parseInt(process.env.BATCH_SIZE) || 50, // Reduced for complex operations
  orgId: process.env.ORG_ID || null, // Required for sl_course_folder
  moduleId: process.env.MODULE_ID || null // Required for sl_course_module
};

// Initialize Supabase client
const supabase = createClient(config.supabaseUrl, config.supabaseKey);

// Cache for avoiding duplicate inserts
const cache = {
  courses: new Map(),      // categoryName -> course_id
  sections: new Map(),     // subCategoryName -> section_id
  folders: new Map(),      // chapterName -> folder_id
  resources: new Map()     // title -> resource_id
};

// Data transformation functions
function transformCsvRow(row) {
  return {
    categoryName: row.categoryName?.trim() || null,
    chapterName: row.chapterName?.trim() || null,
    subCategoryName: row.subCategoryName?.trim() || null,
    title: row.title?.trim() || null,
    url: row.url?.trim() || null,
    embedUrl: row.embedUrl?.trim() || null,
    video_id: row.video_id?.trim() || null,
    thumbnail_url: row.thumbnail_url?.trim() || null,
    isPremium: row.isPremium === 'True',
    isPublished: row.isPublished === 'True',
    author_url: row.author_url?.trim() || null,
    created_at: row.created_at ? new Date(parseInt(row.created_at)).toISOString() : null
  };
}

function validateRow(row, index) {
  const errors = [];

  if (!row.title) {
    errors.push(`Row ${index}: Missing title`);
  }

  if (!row.categoryName) {
    errors.push(`Row ${index}: Missing categoryName`);
  }

  if (!row.subCategoryName) {
    errors.push(`Row ${index}: Missing subCategoryName`);
  }

  if (!row.chapterName) {
    errors.push(`Row ${index}: Missing chapterName`);
  }

  if (!row.embedUrl) {
    errors.push(`Row ${index}: Missing embedUrl`);
  }

  return errors;
}

// Step 1: Insert or get course (categoryName)
async function insertOrGetCourse(categoryName) {
  if (cache.courses.has(categoryName)) {
    return cache.courses.get(categoryName);
  }

  // Check if course already exists
  const { data: existing } = await supabase
    .from('smart_learn.sl_course')
    .select('id')
    .eq('short_name', categoryName)
    .single();

  if (existing) {
    cache.courses.set(categoryName, existing.id);
    return existing.id;
  }

  // Insert new course
  const { data, error } = await supabase
    .from('smart_learn.sl_course')
    .insert({
      short_name: categoryName,
      full_name: categoryName // Using same value for both
    })
    .select('id')
    .single();

  if (error) throw error;

  cache.courses.set(categoryName, data.id);
  return data.id;
}

// Step 2: Insert or get section (subCategoryName)
async function insertOrGetSection(subCategoryName, courseId) {
  const cacheKey = `${subCategoryName}_${courseId}`;
  if (cache.sections.has(cacheKey)) {
    return cache.sections.get(cacheKey);
  }

  // Check if section already exists
  const { data: existing } = await supabase
    .from('smart_learn.sl_section')
    .select('id')
    .eq('name', subCategoryName)
    .eq('course_id', courseId)
    .single();

  if (existing) {
    cache.sections.set(cacheKey, existing.id);
    return existing.id;
  }

  // Insert new section
  const { data, error } = await supabase
    .from('smart_learn.sl_section')
    .insert({
      course_id: courseId,
      name: subCategoryName
    })
    .select('id')
    .single();

  if (error) throw error;

  cache.sections.set(cacheKey, data.id);
  return data.id;
}

// Step 3: Insert or get resource URL (title, url, embedUrl)
async function insertOrGetResource(row, courseId) {
  const cacheKey = `${row.title}_${courseId}`;
  if (cache.resources.has(cacheKey)) {
    return cache.resources.get(cacheKey);
  }

  // Check if resource already exists
  const { data: existing } = await supabase
    .from('smart_learn.sl_res_url')
    .select('id')
    .eq('name', row.title)
    .eq('course_id', courseId)
    .single();

  if (existing) {
    cache.resources.set(cacheKey, existing.id);
    return existing.id;
  }

  // Insert new resource
  const { data, error } = await supabase
    .from('smart_learn.sl_res_url')
    .insert({
      name: row.title,
      description: `Video: ${row.title}`,
      external_url: row.url || row.embedUrl,
      course_id: courseId
    })
    .select('id')
    .single();

  if (error) throw error;

  cache.resources.set(cacheKey, data.id);
  return data.id;
}

// Step 4: Insert or get folder (chapterName)
async function insertOrGetFolder(chapterName, courseId, sectionId) {
  if (!config.orgId) {
    throw new Error('ORG_ID environment variable is required for folder creation');
  }

  const cacheKey = `${chapterName}_${courseId}_${sectionId}`;
  if (cache.folders.has(cacheKey)) {
    return cache.folders.get(cacheKey);
  }

  // Check if folder already exists
  const { data: existing } = await supabase
    .from('smart_learn.sl_course_folder')
    .select('id')
    .eq('folder_name', chapterName)
    .eq('course_id', courseId)
    .eq('section_id', sectionId)
    .single();

  if (existing) {
    cache.folders.set(cacheKey, existing.id);
    return existing.id;
  }

  // Insert new folder
  const { data, error } = await supabase
    .from('smart_learn.sl_course_folder')
    .insert({
      folder_name: chapterName,
      description: `Chapter: ${chapterName}`,
      org_id: config.orgId,
      course_id: courseId,
      section_id: sectionId
    })
    .select('id')
    .single();

  if (error) throw error;

  cache.folders.set(cacheKey, data.id);
  return data.id;
}

// Step 5: Insert course module (final step)
async function insertCourseModule(courseId, sectionId, resourceId, folderId) {
  if (!config.moduleId) {
    throw new Error('MODULE_ID environment variable is required for course module creation');
  }

  // Check if module already exists
  const { data: existing } = await supabase
    .from('smart_learn.sl_course_module')
    .select('id')
    .eq('course_id', courseId)
    .eq('section_id', sectionId)
    .eq('instance', resourceId)
    .single();

  if (existing) {
    return existing.id;
  }

  // Insert new course module
  const { data, error } = await supabase
    .from('smart_learn.sl_course_module')
    .insert({
      course_id: courseId,
      module_id: config.moduleId,
      instance: resourceId,
      section_id: sectionId,
      folder_id: folderId
    })
    .select('id')
    .single();

  if (error) throw error;

  return data.id;
}

// Process a single CSV row through all steps
async function processRow(row, index) {
  try {
    // Step 1: Insert/get course
    const courseId = await insertOrGetCourse(row.categoryName);

    // Step 2: Insert/get section
    const sectionId = await insertOrGetSection(row.subCategoryName, courseId);

    // Step 3: Insert/get resource
    const resourceId = await insertOrGetResource(row, courseId);

    // Step 4: Insert/get folder
    const folderId = await insertOrGetFolder(row.chapterName, courseId, sectionId);

    // Step 5: Insert course module
    const moduleId = await insertCourseModule(courseId, sectionId, resourceId, folderId);

    return {
      success: true,
      courseId,
      sectionId,
      resourceId,
      folderId,
      moduleId
    };
  } catch (error) {
    console.error(`Error processing row ${index}:`, error);
    return {
      success: false,
      error: error.message,
      row: row
    };
  }
}

// Batch processing function
async function processBatch(batch, batchNumber) {
  console.log(`Processing batch ${batchNumber} (${batch.length} records)...`);

  const results = [];
  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < batch.length; i++) {
    const result = await processRow(batch[i].data, batch[i].index);
    results.push(result);

    if (result.success) {
      successCount++;
    } else {
      errorCount++;
    }

    // Progress indicator
    if ((i + 1) % 10 === 0) {
      console.log(`  Progress: ${i + 1}/${batch.length} records processed`);
    }
  }

  console.log(`✅ Batch ${batchNumber} completed: ${successCount} success, ${errorCount} errors`);

  return {
    success: errorCount === 0,
    successCount,
    errorCount,
    results
  };
}

// Main import function
async function importCsvToSupabase() {
  console.log('🚀 Starting CSV import to Supabase...');
  console.log(`📁 CSV file: ${config.csvFilePath}`);
  console.log(`📦 Batch size: ${config.batchSize}`);

  // Validate required configuration
  if (!config.orgId) {
    console.error('❌ ORG_ID environment variable is required');
    process.exit(1);
  }

  if (!config.moduleId) {
    console.error('❌ MODULE_ID environment variable is required');
    process.exit(1);
  }

  // Check if CSV file exists
  if (!fs.existsSync(config.csvFilePath)) {
    console.error(`❌ CSV file not found: ${config.csvFilePath}`);
    process.exit(1);
  }

  const batchResults = [];
  const allErrors = [];
  let batch = [];
  let batchNumber = 1;
  let totalProcessed = 0;
  let totalSuccess = 0;
  let totalErrors = 0;
  
  return new Promise((resolve, reject) => {
    fs.createReadStream(config.csvFilePath)
      .pipe(csv())
      .on('data', async (row) => {
        try {
          // Transform the row
          const transformedRow = transformCsvRow(row);

          // Validate the row
          const validationErrors = validateRow(transformedRow, totalProcessed + 1);
          if (validationErrors.length > 0) {
            allErrors.push(...validationErrors);
            totalErrors++;
            totalProcessed++;
            return;
          }

          // Add to current batch
          batch.push({
            data: transformedRow,
            index: totalProcessed + 1
          });
          totalProcessed++;

          // Process batch when it reaches the configured size
          if (batch.length >= config.batchSize) {
            const result = await processBatch(batch, batchNumber);
            batchResults.push(result);

            totalSuccess += result.successCount;
            totalErrors += result.errorCount;

            // Collect errors from failed records
            result.results
              .filter(r => !r.success)
              .forEach(r => allErrors.push(`Row ${r.row?.title || 'unknown'}: ${r.error}`));

            batch = [];
            batchNumber++;
          }
        } catch (err) {
          console.error(`Error processing row ${totalProcessed + 1}:`, err);
          allErrors.push(`Row ${totalProcessed + 1}: ${err.message}`);
          totalErrors++;
        }
      })
      .on('end', async () => {
        try {
          // Process remaining records in the last batch
          if (batch.length > 0) {
            const result = await processBatch(batch, batchNumber);
            batchResults.push(result);

            totalSuccess += result.successCount;
            totalErrors += result.errorCount;

            // Collect errors from failed records
            result.results
              .filter(r => !r.success)
              .forEach(r => allErrors.push(`Row ${r.row?.title || 'unknown'}: ${r.error}`));
          }

          // Summary
          const successfulBatches = batchResults.filter(r => r.success).length;
          const failedBatches = batchResults.filter(r => !r.success).length;

          console.log('\n📊 Import Summary:');
          console.log(`✅ Total records processed: ${totalProcessed}`);
          console.log(`✅ Total records imported: ${totalSuccess}`);
          console.log(`❌ Total errors: ${totalErrors}`);
          console.log(`✅ Successful batches: ${successfulBatches}`);
          console.log(`❌ Failed batches: ${failedBatches}`);

          // Cache statistics
          console.log('\n📈 Cache Statistics:');
          console.log(`  Courses cached: ${cache.courses.size}`);
          console.log(`  Sections cached: ${cache.sections.size}`);
          console.log(`  Folders cached: ${cache.folders.size}`);
          console.log(`  Resources cached: ${cache.resources.size}`);

          if (allErrors.length > 0) {
            console.log('\n❌ Errors:');
            allErrors.slice(0, 10).forEach(error => console.log(`  - ${error}`));
            if (allErrors.length > 10) {
              console.log(`  ... and ${allErrors.length - 10} more errors`);
            }
          }

          console.log('\n🎉 Import completed!');
          resolve({
            totalProcessed,
            totalSuccess,
            totalErrors,
            successfulBatches,
            failedBatches,
            cacheStats: {
              courses: cache.courses.size,
              sections: cache.sections.size,
              folders: cache.folders.size,
              resources: cache.resources.size
            }
          });
        } catch (err) {
          reject(err);
        }
      })
      .on('error', (err) => {
        console.error('❌ Error reading CSV file:', err);
        reject(err);
      });
  });
}

// Run the import if this file is executed directly
if (require.main === module) {
  importCsvToSupabase()
    .then((summary) => {
      console.log('Import completed successfully:', summary);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Import failed:', error);
      process.exit(1);
    });
}

module.exports = {
  importCsvToSupabase,
  transformCsvRow,
  validateRow,
  insertOrGetCourse,
  insertOrGetSection,
  insertOrGetResource,
  insertOrGetFolder,
  insertCourseModule,
  processRow
};
