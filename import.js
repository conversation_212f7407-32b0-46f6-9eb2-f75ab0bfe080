#!/usr/bin/env node

const fs = require('fs');
const csv = require('csv-parser');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const config = {
  supabaseUrl: process.env.SUPABASE_URL,
  supabaseKey: process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY,
  csvFilePath: process.env.CSV_FILE_PATH || './csv-source',
  targetTable: process.env.TARGET_TABLE || 'courses',
  batchSize: parseInt(process.env.BATCH_SIZE) || 100
};

// Initialize Supabase client
const supabase = createClient(config.supabaseUrl, config.supabaseKey);

// Data transformation functions
function transformCsvRow(row) {
  return {
    category_name: row.categoryName?.trim() || null,
    chapter_name: row.chapterName?.trim() || null,
    sub_category_name: row.subCategoryName?.trim() || null,
    title: row.title?.trim() || null,
    url: row.url?.trim() || null,
    embed_url: row.embedUrl?.trim() || null,
    video_id: row.video_id?.trim() || null,
    thumbnail_url: row.thumbnail_url?.trim() || null,
    is_premium: row.isPremium === 'True',
    is_published: row.isPublished === 'True',
    author_url: row.author_url?.trim() || null,
    created_at: row.created_at ? new Date(parseInt(row.created_at)).toISOString() : null
  };
}

function validateRow(row, index) {
  const errors = [];
  
  if (!row.title) {
    errors.push(`Row ${index}: Missing title`);
  }
  
  if (!row.video_id) {
    errors.push(`Row ${index}: Missing video_id`);
  }
  
  if (!row.embed_url) {
    errors.push(`Row ${index}: Missing embed_url`);
  }
  
  return errors;
}

// Batch processing function
async function insertBatch(batch, batchNumber) {
  try {
    console.log(`Processing batch ${batchNumber} (${batch.length} records)...`);
    
    const { data, error } = await supabase
      .from(config.targetTable)
      .insert(batch)
      .select();
    
    if (error) {
      console.error(`Error in batch ${batchNumber}:`, error);
      return { success: false, error };
    }
    
    console.log(`✅ Batch ${batchNumber} completed successfully (${data.length} records inserted)`);
    return { success: true, count: data.length };
  } catch (err) {
    console.error(`Exception in batch ${batchNumber}:`, err);
    return { success: false, error: err };
  }
}

// Main import function
async function importCsvToSupabase() {
  console.log('🚀 Starting CSV import to Supabase...');
  console.log(`📁 CSV file: ${config.csvFilePath}`);
  console.log(`🎯 Target table: ${config.targetTable}`);
  console.log(`📦 Batch size: ${config.batchSize}`);
  
  // Check if CSV file exists
  if (!fs.existsSync(config.csvFilePath)) {
    console.error(`❌ CSV file not found: ${config.csvFilePath}`);
    process.exit(1);
  }
  
  const results = [];
  const errors = [];
  let batch = [];
  let batchNumber = 1;
  let totalProcessed = 0;
  let totalErrors = 0;
  
  return new Promise((resolve, reject) => {
    fs.createReadStream(config.csvFilePath)
      .pipe(csv())
      .on('data', async (row) => {
        try {
          // Transform the row
          const transformedRow = transformCsvRow(row);
          
          // Validate the row
          const validationErrors = validateRow(transformedRow, totalProcessed + 1);
          if (validationErrors.length > 0) {
            errors.push(...validationErrors);
            totalErrors++;
            return;
          }
          
          // Add to current batch
          batch.push(transformedRow);
          totalProcessed++;
          
          // Process batch when it reaches the configured size
          if (batch.length >= config.batchSize) {
            const result = await insertBatch(batch, batchNumber);
            results.push(result);
            
            batch = [];
            batchNumber++;
          }
        } catch (err) {
          console.error(`Error processing row ${totalProcessed + 1}:`, err);
          errors.push(`Row ${totalProcessed + 1}: ${err.message}`);
          totalErrors++;
        }
      })
      .on('end', async () => {
        try {
          // Process remaining records in the last batch
          if (batch.length > 0) {
            const result = await insertBatch(batch, batchNumber);
            results.push(result);
          }
          
          // Summary
          const successfulBatches = results.filter(r => r.success).length;
          const failedBatches = results.filter(r => !r.success).length;
          const totalInserted = results
            .filter(r => r.success)
            .reduce((sum, r) => sum + (r.count || 0), 0);
          
          console.log('\n📊 Import Summary:');
          console.log(`✅ Total records processed: ${totalProcessed}`);
          console.log(`✅ Total records inserted: ${totalInserted}`);
          console.log(`❌ Total errors: ${totalErrors}`);
          console.log(`✅ Successful batches: ${successfulBatches}`);
          console.log(`❌ Failed batches: ${failedBatches}`);
          
          if (errors.length > 0) {
            console.log('\n❌ Validation Errors:');
            errors.forEach(error => console.log(`  - ${error}`));
          }
          
          if (failedBatches > 0) {
            console.log('\n❌ Failed Batches:');
            results
              .filter(r => !r.success)
              .forEach((r, i) => console.log(`  - Batch ${i + 1}: ${r.error.message || r.error}`));
          }
          
          console.log('\n🎉 Import completed!');
          resolve({
            totalProcessed,
            totalInserted,
            totalErrors,
            successfulBatches,
            failedBatches
          });
        } catch (err) {
          reject(err);
        }
      })
      .on('error', (err) => {
        console.error('❌ Error reading CSV file:', err);
        reject(err);
      });
  });
}

// Run the import if this file is executed directly
if (require.main === module) {
  importCsvToSupabase()
    .then((summary) => {
      console.log('Import completed successfully:', summary);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Import failed:', error);
      process.exit(1);
    });
}

module.exports = { importCsvToSupabase, transformCsvRow, validateRow };
