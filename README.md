# CSV to Supabase Course Data Importer

A Node.js script to import course data from CSV files into a normalized Supabase database schema with validation, error handling, and batch processing.

## Features

- ✅ CSV parsing and data transformation
- ✅ Normalized database schema support (5-table structure)
- ✅ Data validation before import
- ✅ Batch processing for large datasets
- ✅ Intelligent caching to avoid duplicate inserts
- ✅ Error handling and progress tracking
- ✅ Environment-based configuration
- ✅ Detailed import summary and reporting

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Supabase project with appropriate database tables
- CSV file with course data

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

4. Configure your environment variables in `.env`:

   ```env
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   CSV_FILE_PATH=./csv-source
   BATCH_SIZE=50
   ORG_ID=your_organization_uuid
   MODULE_ID=your_module_uuid
   ```

## CSV File Format

The script expects a CSV file with the following columns:

| Column | Description | Example |
|--------|-------------|---------|
| `categoryName` | Course category | "Statistical Assistant" |
| `chapterName` | Chapter/module name | "Real Analysis" |
| `subCategoryName` | Subject area | "Mathematics" |
| `title` | Video/lesson title | "M4 C1 Properties of real No's" |
| `url` | YouTube watch URL | "https://www.youtube.com/watch?v=..." |
| `embedUrl` | YouTube embed URL | "https://www.youtube.com/embed/..." |
| `video_id` | YouTube video ID | "Mu6vqrGP7qM" |
| `thumbnail_url` | YouTube thumbnail URL | "https://i.ytimg.com/vi/..." |
| `isPremium` | Premium content flag | "True" or "False" |
| `isPublished` | Published status | "True" or "False" |
| `author_url` | YouTube channel URL | "https://www.youtube.com/@..." |
| `created_at` | Creation timestamp | Unix timestamp in milliseconds |

## Database Schema

The script works with a normalized database schema consisting of 5 related tables:

### Import Process Flow

1. **Step 1**: Insert `categoryName` → `smart_learn.sl_course` table
2. **Step 2**: Insert `subCategoryName` → `smart_learn.sl_section` table
3. **Step 3**: Insert `title`, `url`, `embedUrl` → `smart_learn.sl_res_url` table
4. **Step 4**: Insert `chapterName` → `smart_learn.sl_course_folder` table
5. **Final Step**: Insert relationships → `smart_learn.sl_course_module` table

### Required Tables

The script expects these tables to exist in your Supabase database. See `database-schema.sql` for the complete schema, or refer to your `db-script.js` file.

### Required Configuration

- **ORG_ID**: UUID for your organization (required for folder creation)
- **MODULE_ID**: UUID for the module type (required for course module creation)

## Usage

### Basic Import

```bash
npm start
```

### Development Mode (with auto-restart)

```bash
npm run dev
```

### Custom Configuration

You can override environment variables:

```bash
CSV_FILE_PATH=./my-data.csv TARGET_TABLE=my_courses npm start
```

## Configuration Options

| Environment Variable | Default | Description |
|---------------------|---------|-------------|
| `SUPABASE_URL` | - | Your Supabase project URL |
| `SUPABASE_ANON_KEY` | - | Supabase anonymous key |
| `SUPABASE_SERVICE_ROLE_KEY` | - | Supabase service role key (recommended) |
| `CSV_FILE_PATH` | `./csv-source` | Path to the CSV file |
| `BATCH_SIZE` | `50` | Number of records to process in each batch |
| `ORG_ID` | - | **Required** Organization UUID for folder creation |
| `MODULE_ID` | - | **Required** Module UUID for course module creation |

## Data Transformation

The script automatically transforms CSV data:

- Trims whitespace from text fields
- Converts `isPremium` and `isPublished` from "True"/"False" strings to booleans
- Converts `created_at` from Unix timestamp to ISO date string
- Maps CSV column names to database-friendly snake_case names

## Error Handling

The script includes comprehensive error handling:

- **Validation Errors**: Missing required fields (title, video_id, embed_url)
- **Database Errors**: Connection issues, constraint violations, etc.
- **File Errors**: Missing CSV file, parsing errors
- **Batch Processing**: Failed batches are reported separately

## Output Example

```text
🚀 Starting CSV import to Supabase...
📁 CSV file: ./csv-source
📦 Batch size: 50

Processing batch 1 (50 records)...
  Progress: 10/50 records processed
  Progress: 20/50 records processed
  Progress: 30/50 records processed
  Progress: 40/50 records processed
  Progress: 50/50 records processed
✅ Batch 1 completed: 50 success, 0 errors

Processing batch 2 (50 records)...
✅ Batch 2 completed: 50 success, 0 errors

📊 Import Summary:
✅ Total records processed: 197
✅ Total records imported: 197
❌ Total errors: 0
✅ Successful batches: 4
❌ Failed batches: 0

📈 Cache Statistics:
  Courses cached: 1
  Sections cached: 1
  Folders cached: 1
  Resources cached: 197

🎉 Import completed!
```

## Troubleshooting

### Common Issues

1. **"CSV file not found"**
   - Check the `CSV_FILE_PATH` in your `.env` file
   - Ensure the file exists and is readable

2. **"Invalid Supabase credentials"**
   - Verify your `SUPABASE_URL` and API keys
   - Ensure the service role key has appropriate permissions

3. **"Table does not exist"**
   - Create the target table in your Supabase database
   - Check the `TARGET_TABLE` configuration

4. **"Column does not exist"**
   - Ensure your database schema matches the expected structure
   - Modify the `transformCsvRow` function if needed

### Performance Tips

- Increase `BATCH_SIZE` for faster imports (but watch for memory usage)
- Use the service role key instead of anon key for better performance
- Consider adding database indexes after import for better query performance

## Development

To modify the script for different CSV formats or database schemas:

1. Update the `transformCsvRow` function in `import.js`
2. Modify the `validateRow` function for different validation rules
3. Adjust the database schema as needed

## License

MIT License - feel free to use and modify as needed.
