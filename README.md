# CSV to Supabase Course Data Importer

A Node.js script to import course data from CSV files into a Supabase database with validation, error handling, and batch processing.

## Features

- ✅ CSV parsing and data transformation
- ✅ Data validation before import
- ✅ Batch processing for large datasets
- ✅ Error handling and progress tracking
- ✅ Environment-based configuration
- ✅ Detailed import summary and reporting

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Supabase project with appropriate database tables
- CSV file with course data

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

4. Configure your environment variables in `.env`:
   ```env
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   CSV_FILE_PATH=./csv-source
   BATCH_SIZE=100
   TARGET_TABLE=courses
   ```

## CSV File Format

The script expects a CSV file with the following columns:

| Column | Description | Example |
|--------|-------------|---------|
| `categoryName` | Course category | "Statistical Assistant" |
| `chapterName` | Chapter/module name | "Real Analysis" |
| `subCategoryName` | Subject area | "Mathematics" |
| `title` | Video/lesson title | "M4 C1 Properties of real No's" |
| `url` | YouTube watch URL | "https://www.youtube.com/watch?v=..." |
| `embedUrl` | YouTube embed URL | "https://www.youtube.com/embed/..." |
| `video_id` | YouTube video ID | "Mu6vqrGP7qM" |
| `thumbnail_url` | YouTube thumbnail URL | "https://i.ytimg.com/vi/..." |
| `isPremium` | Premium content flag | "True" or "False" |
| `isPublished` | Published status | "True" or "False" |
| `author_url` | YouTube channel URL | "https://www.youtube.com/@..." |
| `created_at` | Creation timestamp | Unix timestamp in milliseconds |

## Database Schema

The script assumes a target table with the following structure (adjust as needed):

```sql
CREATE TABLE courses (
  id SERIAL PRIMARY KEY,
  category_name TEXT,
  chapter_name TEXT,
  sub_category_name TEXT,
  title TEXT NOT NULL,
  url TEXT,
  embed_url TEXT NOT NULL,
  video_id TEXT NOT NULL,
  thumbnail_url TEXT,
  is_premium BOOLEAN DEFAULT FALSE,
  is_published BOOLEAN DEFAULT TRUE,
  author_url TEXT,
  created_at TIMESTAMPTZ,
  imported_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Usage

### Basic Import

```bash
npm start
```

### Development Mode (with auto-restart)

```bash
npm run dev
```

### Custom Configuration

You can override environment variables:

```bash
CSV_FILE_PATH=./my-data.csv TARGET_TABLE=my_courses npm start
```

## Configuration Options

| Environment Variable | Default | Description |
|---------------------|---------|-------------|
| `SUPABASE_URL` | - | Your Supabase project URL |
| `SUPABASE_ANON_KEY` | - | Supabase anonymous key |
| `SUPABASE_SERVICE_ROLE_KEY` | - | Supabase service role key (recommended) |
| `CSV_FILE_PATH` | `./csv-source` | Path to the CSV file |
| `TARGET_TABLE` | `courses` | Target database table name |
| `BATCH_SIZE` | `100` | Number of records to process in each batch |

## Data Transformation

The script automatically transforms CSV data:

- Trims whitespace from text fields
- Converts `isPremium` and `isPublished` from "True"/"False" strings to booleans
- Converts `created_at` from Unix timestamp to ISO date string
- Maps CSV column names to database-friendly snake_case names

## Error Handling

The script includes comprehensive error handling:

- **Validation Errors**: Missing required fields (title, video_id, embed_url)
- **Database Errors**: Connection issues, constraint violations, etc.
- **File Errors**: Missing CSV file, parsing errors
- **Batch Processing**: Failed batches are reported separately

## Output Example

```
🚀 Starting CSV import to Supabase...
📁 CSV file: ./csv-source
🎯 Target table: courses
📦 Batch size: 100

Processing batch 1 (100 records)...
✅ Batch 1 completed successfully (100 records inserted)
Processing batch 2 (97 records)...
✅ Batch 2 completed successfully (97 records inserted)

📊 Import Summary:
✅ Total records processed: 197
✅ Total records inserted: 197
❌ Total errors: 0
✅ Successful batches: 2
❌ Failed batches: 0

🎉 Import completed!
```

## Troubleshooting

### Common Issues

1. **"CSV file not found"**
   - Check the `CSV_FILE_PATH` in your `.env` file
   - Ensure the file exists and is readable

2. **"Invalid Supabase credentials"**
   - Verify your `SUPABASE_URL` and API keys
   - Ensure the service role key has appropriate permissions

3. **"Table does not exist"**
   - Create the target table in your Supabase database
   - Check the `TARGET_TABLE` configuration

4. **"Column does not exist"**
   - Ensure your database schema matches the expected structure
   - Modify the `transformCsvRow` function if needed

### Performance Tips

- Increase `BATCH_SIZE` for faster imports (but watch for memory usage)
- Use the service role key instead of anon key for better performance
- Consider adding database indexes after import for better query performance

## Development

To modify the script for different CSV formats or database schemas:

1. Update the `transformCsvRow` function in `import.js`
2. Modify the `validateRow` function for different validation rules
3. Adjust the database schema as needed

## License

MIT License - feel free to use and modify as needed.
