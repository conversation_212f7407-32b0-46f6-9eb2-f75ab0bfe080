#!/usr/bin/env node

const fs = require('fs');
const { createClient } = require('@supabase/supabase-js');
const { transformCsvRow, validateRow } = require('./import.js');
require('dotenv').config();

// Test configuration
async function testConfiguration() {
  console.log('🧪 Testing configuration...');
  
  const config = {
    supabaseUrl: process.env.SUPABASE_URL,
    supabaseKey: process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY,
    csvFilePath: process.env.CSV_FILE_PATH || './csv-source',
    orgId: process.env.ORG_ID,
    moduleId: process.env.MODULE_ID
  };
  
  // Check environment variables
  if (!config.supabaseUrl) {
    console.error('❌ SUPABASE_URL not configured');
    return false;
  }
  
  if (!config.supabaseKey) {
    console.error('❌ Supabase API key not configured');
    return false;
  }

  if (!config.orgId) {
    console.error('❌ ORG_ID not configured (required for folder creation)');
    return false;
  }

  if (!config.moduleId) {
    console.error('❌ MODULE_ID not configured (required for course module creation)');
    return false;
  }

  console.log('✅ Environment variables configured');
  
  // Check CSV file
  if (!fs.existsSync(config.csvFilePath)) {
    console.error(`❌ CSV file not found: ${config.csvFilePath}`);
    return false;
  }
  
  console.log('✅ CSV file found');
  
  // Test Supabase connection and tables
  try {
    const supabase = createClient(config.supabaseUrl, config.supabaseKey);

    // Test each required table
    const tables = [
      'smart_learn.sl_course',
      'smart_learn.sl_section',
      'smart_learn.sl_res_url',
      'smart_learn.sl_course_folder',
      'smart_learn.sl_course_module'
    ];

    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);

      if (error) {
        console.error(`❌ Table '${table}' not accessible: ${error.message}`);
        console.log('💡 Make sure all required tables exist and API key has proper permissions');
        return false;
      }

      console.log(`✅ Table '${table}' accessible`);
    }

    console.log('✅ Database connection successful');

  } catch (err) {
    console.error(`❌ Supabase connection error: ${err.message}`);
    return false;
  }
  
  return true;
}

// Test data transformation
function testDataTransformation() {
  console.log('\n🧪 Testing data transformation...');
  
  const sampleCsvRow = {
    categoryName: 'Statistical Assistant ',
    chapterName: 'Real Analysis',
    subCategoryName: 'Mathematics ',
    title: 'M4 C1 Properties of real No\'s',
    url: '',
    embedUrl: 'https://www.youtube.com/embed/Mu6vqrGP7qM?feature=oembed',
    video_id: 'Mu6vqrGP7qM',
    thumbnail_url: 'https://i.ytimg.com/vi/Mu6vqrGP7qM/hqdefault.jpg',
    isPremium: 'True',
    isPublished: 'True',
    author_url: 'https://www.youtube.com/@c-educore851',
    created_at: '1749446961921'
  };
  
  try {
    const transformed = transformCsvRow(sampleCsvRow);
    
    console.log('Sample transformation:');
    console.log('Input:', sampleCsvRow);
    console.log('Output:', transformed);
    
    // Validate transformation
    const errors = validateRow(transformed, 1);
    if (errors.length > 0) {
      console.error('❌ Validation errors:', errors);
      return false;
    }
    
    console.log('✅ Data transformation working correctly');
    return true;
    
  } catch (err) {
    console.error(`❌ Transformation error: ${err.message}`);
    return false;
  }
}

// Test CSV parsing
function testCsvParsing() {
  console.log('\n🧪 Testing CSV parsing...');
  
  const csv = require('csv-parser');
  const csvFilePath = process.env.CSV_FILE_PATH || './csv-source';
  
  return new Promise((resolve) => {
    let rowCount = 0;
    let sampleRows = [];
    
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        rowCount++;
        if (sampleRows.length < 3) {
          sampleRows.push(row);
        }
      })
      .on('end', () => {
        console.log(`✅ CSV parsing successful - ${rowCount} rows found`);
        console.log('Sample rows:');
        sampleRows.forEach((row, i) => {
          console.log(`  Row ${i + 1}:`, Object.keys(row));
        });
        resolve(true);
      })
      .on('error', (err) => {
        console.error(`❌ CSV parsing error: ${err.message}`);
        resolve(false);
      });
  });
}

// Main test function
async function runTests() {
  console.log('🚀 Running import script tests...\n');
  
  const configTest = await testConfiguration();
  const transformTest = testDataTransformation();
  const csvTest = await testCsvParsing();
  
  console.log('\n📊 Test Results:');
  console.log(`Configuration: ${configTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Data Transformation: ${transformTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`CSV Parsing: ${csvTest ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = configTest && transformTest && csvTest;
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! Ready to run the import.');
    console.log('Run: npm start');
  } else {
    console.log('\n❌ Some tests failed. Please fix the issues before running the import.');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch((error) => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}
