{"name": "csv-to-supabase-importer", "version": "1.0.0", "description": "Node.js script to import course data from CSV to Supabase database", "main": "import.js", "scripts": {"start": "node import.js", "dev": "node --watch import.js", "test": "node test.js"}, "keywords": ["csv", "supabase", "import", "course-data", "database"], "author": "", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "fs": "^0.0.1-security"}, "devDependencies": {"nodemon": "^3.0.2"}}