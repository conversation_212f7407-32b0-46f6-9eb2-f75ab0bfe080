//Step1: insert the categoryName to this table and get Id
create table smart_learn.sl_course (
  id uuid not null default extensions.uuid_generate_v4 (),
  short_name character varying(100) not null,
  full_name character varying(255) not null
)

//Step2: insert the subCategoryName to this table and get the Id 
create table smart_learn.sl_section (
  id uuid not null default extensions.uuid_generate_v4 (),
  course_id uuid not null,
  name character varying(255) not null
)

//Step3: insert the title,url,embedUrl to this table and get the Id
create table smart_learn.sl_res_url (
  id uuid not null default gen_random_uuid (),
  name text not null,
  description text null,
  external_url text null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  course_id uuid null
)

//Step4: then insert the chapterName to this table and get the Id
create table smart_learn.sl_course_folder (
  id uuid not null default gen_random_uuid (),
  folder_name text not null,
  description text null default ''::text,
  org_id uuid not null,
  course_id uuid not null,
  section_id uuid not null
)

//finally insert to this table with values like: course_id=step1->Id, module_id=“abc”, instance=step3=>Id, section_id=step2=>Id, folder_id = step4=>Id
create table smart_learn.sl_course_module (
  id uuid not null default extensions.uuid_generate_v4 (),
  course_id uuid not null,
  module_id uuid not null,
  instance uuid not null,
  section_id uuid not null,
  folder_id uuid null
)



